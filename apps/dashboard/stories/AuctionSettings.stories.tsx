import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { AuctionSettings, Crud } from '../src/forms/AuctionSettings';
import { 
  createDefaultDeSettingsValue, 
  createEmptyDeSettingsValue, 
  demoAuctionCategories 
} from '../src/forms/AuctionSettings.helpers';

const meta: Meta<typeof AuctionSettings> = {
  title: 'Forms/AuctionSettings',
  component: AuctionSettings,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A comprehensive auction settings form converted from Vue to React. Includes date picker, number inputs, selects, and a dynamic excess levels table.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    crud: {
      control: 'select',
      options: Object.values(Crud),
      description: 'CRUD operation mode - determines if form is editable',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Wrapper component to handle state
function AuctionSettingsWrapper(props: any) {
  const [settings, setSettings] = useState(props.settings);
  
  return (
    <div className="p-4 bg-gray-100 min-h-screen">
      <div className="mb-4">
        <h2 className="text-lg font-bold mb-2">Auction Settings Form</h2>
        <p className="text-sm text-gray-600 mb-4">
          This form allows configuration of auction parameters including timing, pricing, quantities, and excess levels.
        </p>
      </div>
      
      <AuctionSettings
        {...props}
        settings={settings}
        onSettingsChange={setSettings}
        auctionCategories={demoAuctionCategories}
      />
      
      <div className="mt-6 p-4 bg-white rounded border">
        <h3 className="font-bold mb-2">Current Settings (JSON):</h3>
        <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-64">
          {JSON.stringify(settings, null, 2)}
        </pre>
      </div>
    </div>
  );
}

// Default story with populated data
export const Default: Story = {
  render: (args) => <AuctionSettingsWrapper {...args} />,
  args: {
    settings: createDefaultDeSettingsValue(),
    crud: Crud.CREATE,
  },
};

// Editable mode (CREATE/UPDATE)
export const Editable: Story = {
  render: (args) => <AuctionSettingsWrapper {...args} />,
  args: {
    settings: createDefaultDeSettingsValue(),
    crud: Crud.UPDATE,
  },
};

// Read-only mode
export const ReadOnly: Story = {
  render: (args) => <AuctionSettingsWrapper {...args} />,
  args: {
    settings: createDefaultDeSettingsValue(),
    crud: Crud.READ,
  },
};

// Empty form
export const Empty: Story = {
  render: (args) => <AuctionSettingsWrapper {...args} />,
  args: {
    settings: createEmptyDeSettingsValue(),
    crud: Crud.CREATE,
  },
};

// With custom data
export const CustomData: Story = {
  render: (args) => <AuctionSettingsWrapper {...args} />,
  args: {
    settings: {
      ...createDefaultDeSettingsValue(),
      auction_name: "High-Frequency Trading Auction",
      price_label: "Price (¢/kWh)",
      quantity_label: "Power (GW)",
      price_decimal_places: 4,
      price_change_initial: "0.0025",
      price_change_post_reversal: "0.0010",
      round_orange_secs: 10,
      round_red_secs: 5,
      excess_level_1_label: "Low Excess",
      excess_level_2_label: "Medium Excess", 
      excess_level_3_label: "High Excess",
      excess_level_4_label: "Critical Excess",
    },
    crud: Crud.CREATE,
  },
};
