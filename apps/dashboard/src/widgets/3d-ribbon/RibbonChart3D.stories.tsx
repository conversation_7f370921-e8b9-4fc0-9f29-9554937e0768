/**
 * Storybook stories for 3D Ribbon Chart
 */

import type { Meta, StoryObj } from '@storybook/react';
import { RibbonChart3D } from './RibbonChart3D';
import type { DeRoundTraderElement, DeTraderElement } from '@/api-client';
import { OrderType, OrderSubmissionType } from '@/api-client';

const meta: Meta<typeof RibbonChart3D> = {
  title: 'Widgets/3D Ribbon Chart',
  component: RibbonChart3D,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A 3D ribbon chart that visualizes auction bidding data, showing how trader quantities change across rounds. Green ribbons represent buy orders (positive quantities), red ribbons represent sell orders (negative quantities).'
      }
    }
  },
  argTypes: {
    width: { control: { type: 'range', min: 400, max: 1200, step: 50 } },
    height: { control: { type: 'range', min: 300, max: 800, step: 50 } },
    ribbonWidth: { control: { type: 'range', min: 0.1, max: 2, step: 0.1 } },
    singleColor: { control: 'boolean' },
    showValidation: { control: 'boolean' },
    title: { control: 'text' }
  }
};

export default meta;
type Story = StoryObj<typeof RibbonChart3D>;

// Demo data generators
function createDemoTrader(id: string, shortname: string): DeTraderElement {
  return {
    company_id: id,
    shortname,
    id: `trader-${id}`,
    rank: Math.floor(Math.random() * 100),
    has_seen_auction: true
  };
}

function createDemoRoundTrader(
  round: number,
  companyId: string,
  companyShortname: string,
  orderType: OrderType,
  baseQuantity: number,
  variation: number = 0.2
): DeRoundTraderElement {
  // Apply monotonic constraints with some variation
  let quantity = baseQuantity;
  if (orderType === OrderType.BUY) {
    // Buy quantities should decrease over rounds
    quantity = Math.max(10, baseQuantity - (round - 1) * 20 + (Math.random() - 0.5) * variation * baseQuantity);
  } else {
    // Sell quantities should increase over rounds
    quantity = baseQuantity + (round - 1) * 15 + (Math.random() - 0.5) * variation * baseQuantity;
  }

  return {
    id: `rte-${round}-${companyId}`,
    round,
    cid: companyId,
    company_shortname: companyShortname,
    order_type: orderType,
    quantity_int: Math.floor(quantity),
    quantity_str: Math.floor(quantity).toString(),
    match: Math.floor(quantity * 0.8),
    changed: false,
    bid_while_closed: false,
    order_submission_type: OrderSubmissionType.MANUAL,
    order_submitted_by: `user-${companyId}`,
    timestamp_formatted: '2024-01-15 10:30:00',
    constraints: {
      min_buy_quantity: 0,
      max_buy_quantity: 1000,
      min_sell_quantity: 0,
      max_sell_quantity: 1000
    },
    buyer_credit_limit: 1000000,
    buyer_credit_limit_str: '$1,000,000'
  };
}

// Create demo data
const demoTraders: DeTraderElement[] = [
  createDemoTrader('BUYER_A', 'BuyerA'),
  createDemoTrader('BUYER_B', 'BuyerB'),
  createDemoTrader('BUYER_C', 'BuyerC'),
  createDemoTrader('SELLER_X', 'SellerX'),
  createDemoTrader('SELLER_Y', 'SellerY'),
  createDemoTrader('SELLER_Z', 'SellerZ')
];

const demoRoundTraders: DeRoundTraderElement[] = [];

// Generate data for 8 rounds
for (let round = 1; round <= 8; round++) {
  // Buyers with decreasing quantities
  demoRoundTraders.push(createDemoRoundTrader(round, 'BUYER_A', 'BuyerA', OrderType.BUY, 200));
  demoRoundTraders.push(createDemoRoundTrader(round, 'BUYER_B', 'BuyerB', OrderType.BUY, 180));
  demoRoundTraders.push(createDemoRoundTrader(round, 'BUYER_C', 'BuyerC', OrderType.BUY, 160));

  // Sellers with increasing quantities
  demoRoundTraders.push(createDemoRoundTrader(round, 'SELLER_X', 'SellerX', OrderType.SELL, 100));
  demoRoundTraders.push(createDemoRoundTrader(round, 'SELLER_Y', 'SellerY', OrderType.SELL, 120));
  demoRoundTraders.push(createDemoRoundTrader(round, 'SELLER_Z', 'SellerZ', OrderType.SELL, 90));
}

// Basic story
export const Default: Story = {
  args: {
    width: 800,
    height: 600,
    round_trader_elements: demoRoundTraders,
    traders: demoTraders,
    title: '3D Auction Bidding Ribbons',
    ribbonWidth: 0.8,
    singleColor: false,
    showValidation: false
  }
};

// Single color variant
export const SingleColor: Story = {
  args: {
    ...Default.args,
    singleColor: true,
    title: '3D Ribbons - Single Colors'
  }
};

// With validation
export const WithValidation: Story = {
  args: {
    ...Default.args,
    showValidation: true,
    title: '3D Ribbons - With Monotonic Validation'
  }
};

// Narrow ribbons
export const NarrowRibbons: Story = {
  args: {
    ...Default.args,
    ribbonWidth: 0.3,
    title: '3D Ribbons - Narrow Width'
  }
};

// Wide ribbons
export const WideRibbons: Story = {
  args: {
    ...Default.args,
    ribbonWidth: 1.5,
    title: '3D Ribbons - Wide Width'
  }
};

// Large size
export const Large: Story = {
  args: {
    ...Default.args,
    width: 1000,
    height: 700,
    title: '3D Ribbons - Large Size'
  }
};
