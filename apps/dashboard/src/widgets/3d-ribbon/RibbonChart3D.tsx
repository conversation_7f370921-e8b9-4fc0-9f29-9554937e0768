/**
 * 3D Ribbon Chart component using Plotly.js
 * Direct translation from Vue Chart8PlotlyGPTo1.vue
 */

import React, { useState, useEffect, useRef } from 'react';
import Plotly from 'plotly.js';

export interface RibbonChart3DProps {
  width?: number;
  height?: number;
}

export const RibbonChart3D: React.FC<RibbonChart3DProps> = ({
  width = 800,
  height = 600
}) => {
  const [colorscale, setColorscale] = useState('Viridis');
  const [opacity, setOpacity] = useState(1);
  const [ambient, setAmbient] = useState(0.5);
  const [diffuse, setDiffuse] = useState(0.5);
  const [rotationLimit, setRotationLimit] = useState(360);

  const plotRef = useRef<HTMLDivElement>(null);
  const dataRef = useRef<any[]>([]);
  const layoutRef = useRef<any>({});
  const figureDataRef = useRef<any>(null);
  const rotatingRef = useRef(false);
  const relayoutHandlerRef = useRef<any>(null);

  const initPlot = () => {
    if (!plotRef.current || !figureDataRef.current) return;

    dataRef.current = [];
    const maxPoints = 50;

    for (let i = 0; i < 7; i++) {
      const trace = {
        x: figureDataRef.current[i].x.slice(0, maxPoints),
        y: figureDataRef.current[i].y.slice(0, maxPoints),
        z: figureDataRef.current[i].z.slice(0, maxPoints).map((row: any) => row.slice(0, maxPoints)),
        name: '',
        colorscale: colorscale,
        opacity: opacity,
        lighting: {
          ambient: ambient,
          diffuse: diffuse,
          specular: 0.1,
          roughness: 0.9,
          fresnel: 0.2,
        },
        type: 'surface',
        showscale: false,
      };
      dataRef.current.push(trace);
    }

    layoutRef.current = {
      title: 'Ribbon Plot',
      showlegend: false,
      autosize: true,
      width,
      height,
      scene: {
        xaxis: { title: 'Sample #' },
        yaxis: { title: 'Wavelength' },
        zaxis: { title: 'OD' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 1 },
          up: { x: 0, y: 0, z: 1 },
        },
        dragmode: 'turntable',
      },
    };

    Plotly.newPlot(plotRef.current, dataRef.current, layoutRef.current);
    setUpRotationLimit();
  };

  const setUpRotationLimit = () => {
    const myDiv = plotRef.current as any;
    if (!myDiv) return;

    if (relayoutHandlerRef.current) {
      myDiv.removeListener('plotly_relayout', relayoutHandlerRef.current);
    }

    relayoutHandlerRef.current = (eventData: any) => {
      if (eventData['scene.camera'] && !rotatingRef.current) {
        rotatingRef.current = true;
        const camera = eventData['scene.camera'];
        const eye = camera.eye;
        const r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
        let theta = Math.atan2(eye.y, eye.x) * (180 / Math.PI);

        const limitAngle = rotationLimit / 2;
        if (theta > limitAngle) {
          theta = limitAngle;
        } else if (theta < -limitAngle) {
          theta = -limitAngle;
        }

        const newEyeX = r * Math.cos((theta * Math.PI) / 180);
        const newEyeY = r * Math.sin((theta * Math.PI) / 180);

        const update = {
          'scene.camera.eye.x': newEyeX,
          'scene.camera.eye.y': newEyeY,
        };

        Plotly.relayout(plotRef.current, update).then(() => {
          rotatingRef.current = false;
        });
      }
    };

    myDiv.on('plotly_relayout', relayoutHandlerRef.current);
  };

  const updatePlot = () => {
    if (!plotRef.current) return;

    dataRef.current.forEach((trace) => {
      trace.colorscale = colorscale;
      trace.opacity = opacity;
      trace.lighting = {
        ambient: ambient,
        diffuse: diffuse,
        specular: 0.1,
        roughness: 0.9,
        fresnel: 0.2,
      };
    });

    Plotly.react(plotRef.current, dataRef.current, layoutRef.current);
  };

  const resetCamera = () => {
    if (!plotRef.current) return;

    Plotly.relayout(plotRef.current, {
      'scene.camera': {
        eye: { x: 1.5, y: 1.5, z: 1 },
        up: { x: 0, y: 0, z: 1 },
      },
    });
  };

  // Update plot when controls change
  useEffect(() => {
    updatePlot();
  }, [colorscale, opacity, ambient, diffuse]);

  // Initialize plot on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
        const figure = await response.json();
        figureDataRef.current = figure.data;
        initPlot();
      } catch (error) {
        console.error('Failed to fetch ribbon data:', error);
      }
    };

    fetchData();
  }, []);

  return (
    <div style={{ width, height }}>
      <div style={{ marginBottom: '20px' }}>
        <h3>Controls</h3>
        <label style={{ display: 'inline-block', width: '150px' }}>Colorscale:</label>
        <select value={colorscale} onChange={(e) => setColorscale(e.target.value)} style={{ marginBottom: '10px' }}>
          <option value="Viridis">Viridis</option>
          <option value="Cividis">Cividis</option>
          <option value="Hot">Hot</option>
          <option value="Electric">Electric</option>
          <option value="Earth">Earth</option>
        </select>
        <br />
        <label style={{ display: 'inline-block', width: '150px' }}>Opacity:</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={opacity}
          onChange={(e) => setOpacity(parseFloat(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{opacity}</span>
        <br />
        <label style={{ display: 'inline-block', width: '150px' }}>Ambient Lighting:</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={ambient}
          onChange={(e) => setAmbient(parseFloat(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{ambient}</span>
        <br />
        <label style={{ display: 'inline-block', width: '150px' }}>Diffuse Lighting:</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={diffuse}
          onChange={(e) => setDiffuse(parseFloat(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{diffuse}</span>
        <br />
        <label style={{ display: 'inline-block', width: '150px' }}>Rotation Limit (°):</label>
        <input
          type="range"
          min="0"
          max="360"
          step="10"
          value={rotationLimit}
          onChange={(e) => setRotationLimit(parseInt(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{rotationLimit}</span>
        <br /><br />
        <button onClick={resetCamera}>Reset Camera View</button>
      </div>
      <div ref={plotRef} style={{ width: '100%', height: '600px' }} />
    </div>
  );
};

export default RibbonChart3D;
