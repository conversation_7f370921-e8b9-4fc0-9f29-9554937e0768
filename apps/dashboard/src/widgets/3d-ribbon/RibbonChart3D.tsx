/**
 * 3D Ribbon Chart component using Plotly.js
 * Direct translation from Vue Chart8PlotlyGPTo1.vue following docs/requirements/3dribbon-plotly.gemini.md
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Plotly from 'plotly.js';

export interface RibbonChart3DProps {
  width?: number;
  height?: number;
}

export const RibbonChart3D: React.FC<RibbonChart3DProps> = ({
  width = 800,
  height = 600
}) => {
  const [colorscale, setColorscale] = useState('Viridis');
  const [opacity, setOpacity] = useState(1);
  const [ambient, setAmbient] = useState(0.5);
  const [diffuse, setDiffuse] = useState(0.5);
  const [rotationLimit, setRotationLimit] = useState(360);

  const plotDivRef = useRef<HTMLDivElement>(null);
  const figureDataRef = useRef<any>(null);
  const plotDataRef = useRef<any[]>([]);
  const plotLayoutRef = useRef<any>({});
  const rotatingRef = useRef(false);
  const animationIntervalRef = useRef<any>(null);
  const animationCountRef = useRef(0);
  const relayoutHandlerRef = useRef<any>(null);

  const maxIterations = 10;

  // Equivalent to Vue's initPlot
  const initPlot = useCallback(() => {
    if (!figureDataRef.current || !plotDivRef.current) return;

    const newPlotData = [];
    const maxPoints = 50;

    for (let i = 0; i < 7; i++) {
      const trace = {
        x: figureDataRef.current[i].x.slice(0, maxPoints),
        y: figureDataRef.current[i].y.slice(0, maxPoints),
        z: figureDataRef.current[i].z.slice(0, maxPoints).map((row: any) => row.slice(0, maxPoints)),
        name: '',
        colorscale: colorscale,
        opacity: opacity,
        lighting: {
          ambient: ambient,
          diffuse: diffuse,
          specular: 0.1,
          roughness: 0.9,
          fresnel: 0.2,
        },
        type: 'surface',
        showscale: false,
      };
      newPlotData.push(trace);
    }
    plotDataRef.current = newPlotData;

    const newLayout = {
      title: 'Ribbon Plot',
      showlegend: false,
      autosize: true,
      width,
      height,
      scene: {
        xaxis: { title: 'Sample #' },
        yaxis: { title: 'Wavelength' },
        zaxis: { title: 'OD' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 1 },
          up: { x: 0, y: 0, z: 1 },
        },
        dragmode: 'turntable',
      },
    };
    plotLayoutRef.current = newLayout;

    Plotly.newPlot(plotDivRef.current, plotDataRef.current, plotLayoutRef.current);
  }, [colorscale, opacity, ambient, diffuse, width, height]);

  // Equivalent to Vue's setUpRotationLimit
  // This useEffect handles adding/removing the plotly_relayout listener
  useEffect(() => {
    const myDiv = plotDivRef.current;
    if (!myDiv) return;

    const handler = (eventData: any) => {
      if (eventData['scene.camera'] && !rotatingRef.current) {
        rotatingRef.current = true;
        const camera = eventData['scene.camera'];
        const eye = camera.eye;
        const r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
        let theta = Math.atan2(eye.y, eye.x) * (180 / Math.PI);

        const limitAngle = rotationLimit / 2;
        if (theta > limitAngle) {
          theta = limitAngle;
        } else if (theta < -limitAngle) {
          theta = -limitAngle;
        }

        const newEyeX = r * Math.cos((theta * Math.PI) / 180);
        const newEyeY = r * Math.sin((theta * Math.PI) / 180);

        const update = {
          'scene.camera.eye.x': newEyeX,
          'scene.camera.eye.y': newEyeY,
        };

        Plotly.relayout(myDiv, update).then(() => {
          rotatingRef.current = false;
        });
      }
    };

    relayoutHandlerRef.current = handler;
    (myDiv as any).on('plotly_relayout', relayoutHandlerRef.current);

    return () => {
      if (myDiv && relayoutHandlerRef.current) {
        if (typeof (myDiv as any).removeListener === 'function') {
          (myDiv as any).removeListener('plotly_relayout', relayoutHandlerRef.current);
        }
        relayoutHandlerRef.current = null;
      }
    };
  }, [rotationLimit]);

  // Equivalent to Vue's updatePlot (called by watch)
  // This useEffect handles updates when control values change
  useEffect(() => {
    if (!plotDivRef.current || plotDataRef.current.length === 0) return;

    const updatedPlotData = plotDataRef.current.map(trace => ({
      ...trace,
      colorscale: colorscale,
      opacity: opacity,
      lighting: {
        ...trace.lighting,
        ambient: ambient,
        diffuse: diffuse,
      },
    }));
    plotDataRef.current = updatedPlotData;

    Plotly.react(plotDivRef.current, plotDataRef.current, plotLayoutRef.current);
  }, [colorscale, opacity, ambient, diffuse]);

  // Equivalent to Vue's resetCamera
  const resetCamera = () => {
    if (!plotDivRef.current) return;
    Plotly.relayout(plotDivRef.current, {
      'scene.camera': {
        eye: { x: 1.5, y: 1.5, z: 1 },
        up: { x: 0, y: 0, z: 1 },
      },
    });
  };

  // Equivalent to Vue's startAnimation
  const startAnimation = useCallback(() => {
    if (animationIntervalRef.current) clearInterval(animationIntervalRef.current);
    animationCountRef.current = 0;

    animationIntervalRef.current = setInterval(() => {
      if (!plotDivRef.current || plotDataRef.current.length === 0) {
        clearInterval(animationIntervalRef.current);
        return;
      }

      const currentPlotData = plotDataRef.current;
      for (let i = 0; i < currentPlotData.length; i++) {
        for (let j = 0; j < currentPlotData[i].z.length; j++) {
          for (let k = 0; k < currentPlotData[i].z[j].length; k++) {
            currentPlotData[i].z[j][k] += 0.1;
          }
        }
      }

      Plotly.animate(
        plotDivRef.current,
        { data: currentPlotData },
        {
          transition: { duration: 500, easing: 'linear' },
          frame: { duration: 500, redraw: false },
        }
      );

      animationCountRef.current++;
      if (animationCountRef.current >= maxIterations) {
        clearInterval(animationIntervalRef.current);
        animationIntervalRef.current = null;
      }
    }, 1000);
  }, [maxIterations]);

  // Equivalent to Vue's onMounted
  useEffect(() => {
    let isMounted = true;

    const fetchDataAndInit = async () => {
      try {
        const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
        const figure = await response.json();
        if (isMounted) {
          figureDataRef.current = figure.data;
          initPlot();
          startAnimation();
        }
      } catch (error) {
        console.error('Failed to fetch or initialize plot:', error);
      }
    };

    fetchDataAndInit();

    return () => {
      isMounted = false;
      if (animationIntervalRef.current) {
        clearInterval(animationIntervalRef.current);
      }
      if (plotDivRef.current) {
        Plotly.purge(plotDivRef.current);
      }
    };
  }, [initPlot, startAnimation]);

  return (
    <div style={{ width, height }}>
      <div style={{ marginBottom: '20px' }}>
        <h3>Controls</h3>
        <label htmlFor="colorscale" style={{ display: 'inline-block', width: '150px' }}>Colorscale:</label>
        <select
          id="colorscale"
          value={colorscale}
          onChange={(e) => setColorscale(e.target.value)}
          style={{ marginBottom: '10px' }}
        >
          <option value="Viridis">Viridis</option>
          <option value="Cividis">Cividis</option>
          <option value="Hot">Hot</option>
          <option value="Electric">Electric</option>
          <option value="Earth">Earth</option>
        </select>
        <br />
        <label htmlFor="opacity" style={{ display: 'inline-block', width: '150px' }}>Opacity:</label>
        <input
          type="range"
          id="opacity"
          min="0"
          max="1"
          step="0.1"
          value={opacity}
          onChange={(e) => setOpacity(parseFloat(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{opacity.toFixed(1)}</span>
        <br />
        <label htmlFor="ambient" style={{ display: 'inline-block', width: '150px' }}>Ambient Lighting:</label>
        <input
          type="range"
          id="ambient"
          min="0"
          max="1"
          step="0.1"
          value={ambient}
          onChange={(e) => setAmbient(parseFloat(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{ambient.toFixed(1)}</span>
        <br />
        <label htmlFor="diffuse" style={{ display: 'inline-block', width: '150px' }}>Diffuse Lighting:</label>
        <input
          type="range"
          id="diffuse"
          min="0"
          max="1"
          step="0.1"
          value={diffuse}
          onChange={(e) => setDiffuse(parseFloat(e.target.value))}
          style={{ marginBottom: '10px' }}
        />
        <span>{diffuse.toFixed(1)}</span>
        <br />
        <label htmlFor="rotationLimit" style={{ display: 'inline-block', width: '150px' }}>Rotation Limit (°):</label>
        <input
          type="range"
          id="rotationLimit"
          min="0"
          max="360"
          step="10"
          value={rotationLimit}
          onChange={(e) => setRotationLimit(parseInt(e.target.value, 10))}
          style={{ marginBottom: '10px' }}
        />
        <span>{rotationLimit}</span>
        <br /><br />
        <button onClick={resetCamera}>Reset Camera View</button>
      </div>
      <div ref={plotDivRef} style={{ width: '100%', height: '600px' }} />
    </div>
  );
};

export default RibbonChart3D;
