/**
 * Data transformation utilities for 3D ribbon chart
 * Converts round-table data to Plotly surface format
 */

import type { DeRoundTraderElement, DeTraderElement } from '@/api-client';
import { OrderType } from '@/api-client';
import { getBuyColorscale, getSellColorscale } from './ribbon-colors';

export interface RibbonSurfaceData {
  type: 'surface';
  x: number[][];
  y: number[][];
  z: number[][];
  colorscale: Array<[number, string]> | Array<[string, string]>;
  showscale: boolean;
  name: string;
  hovertemplate: string;
}

export interface RibbonChartData {
  surfaces: RibbonSurfaceData[];
  layout: {
    scene: {
      xaxis: { title: string };
      yaxis: { title: string };
      zaxis: { title: string };
    };
  };
}

/**
 * Transform round-trader data into ribbon surfaces
 */
export function transformToRibbonData(
  roundTraderElements: DeRoundTraderElement[],
  traders: DeTraderElement[],
  options: {
    ribbonWidth?: number;
    singleColor?: boolean;
  } = {}
): RibbonChartData {
  const { ribbonWidth = 0.8, singleColor = false } = options;

  const surfaces: RibbonSurfaceData[] = [];

  // Create a map for quick trader lookup
  const traderMap = new Map(traders.map(t => [t.company_id, t]));

  // Group round-trader elements by company
  const traderGroups = new Map<string, DeRoundTraderElement[]>();

  roundTraderElements.forEach(rte => {
    if (!traderGroups.has(rte.cid)) {
      traderGroups.set(rte.cid, []);
    }
    traderGroups.get(rte.cid)!.push(rte);
  });

  // Create surfaces for each trader
  let traderIndex = 0;

  traderGroups.forEach((traderData, companyId) => {
    const trader = traderMap.get(companyId);
    if (!trader) return;

    // Sort by round number
    const sortedData = traderData.sort((a, b) => a.round - b.round);

    // Skip traders with no data
    if (sortedData.length === 0) return;

    // Determine if this trader is primarily a buyer or seller
    // Use the most recent order type, or majority order type
    const lastOrder = sortedData[sortedData.length - 1];
    const orderType = lastOrder.order_type;

    // Create surface coordinates
    const x: number[][] = [];
    const y: number[][] = [];
    const z: number[][] = [];

    sortedData.forEach(rte => {
      // X coordinates: [round, round + width] for ribbon width
      x.push([rte.round, rte.round + ribbonWidth]);

      // Y coordinates: [traderIndex, traderIndex] for consistent trader position
      y.push([traderIndex, traderIndex]);

      // Z coordinates: positive for BUY, negative for SELL
      const quantity = rte.order_type === OrderType.SELL ? -rte.quantity_int : rte.quantity_int;
      z.push([quantity, quantity]);
    });

    // Create surface
    const surface: RibbonSurfaceData = {
      type: 'surface',
      x,
      y,
      z,
      colorscale: orderType === OrderType.BUY
        ? getBuyColorscale(singleColor)
        : getSellColorscale(singleColor),
      showscale: false,
      name: `${trader.shortname} (${orderType})`,
      hovertemplate:
        '<b>%{fullData.name}</b><br>' +
        'Round: %{x}<br>' +
        'Quantity: %{z}<br>' +
        '<extra></extra>'
    };

    surfaces.push(surface);
    traderIndex++;
  });

  return {
    surfaces,
    layout: {
      scene: {
        xaxis: { title: 'Round' },
        yaxis: { title: 'Trader' },
        zaxis: { title: 'Quantity (+ Buy, - Sell)' }
      }
    }
  };
}

/**
 * Get trader labels for Y-axis
 */
export function getTraderLabels(
  roundTraderElements: DeRoundTraderElement[],
  traders: DeTraderElement[]
): string[] {
  const traderMap = new Map(traders.map(t => [t.company_id, t]));
  const uniqueCompanyIds = [...new Set(roundTraderElements.map(rte => rte.cid))];

  return uniqueCompanyIds
    .map(cid => traderMap.get(cid)?.shortname || cid)
    .filter(Boolean);
}

/**
 * Validate monotonic bidding constraints
 */
export function validateMonotonicConstraints(
  roundTraderElements: DeRoundTraderElement[]
): {
  violations: Array<{
    trader: string;
    round: number;
    issue: string;
  }>;
  summary: {
    totalViolations: number;
    buyViolations: number;
    sellViolations: number;
  };
} {
  const violations: Array<{ trader: string; round: number; issue: string }> = [];

  // Group by trader
  const traderGroups = new Map<string, DeRoundTraderElement[]>();
  roundTraderElements.forEach(rte => {
    if (!traderGroups.has(rte.cid)) {
      traderGroups.set(rte.cid, []);
    }
    traderGroups.get(rte.cid)!.push(rte);
  });

  // Check each trader's progression
  traderGroups.forEach((traderData, companyId) => {
    const sortedData = traderData.sort((a, b) => a.round - b.round);

    for (let i = 1; i < sortedData.length; i++) {
      const prev = sortedData[i - 1];
      const curr = sortedData[i];

      // Only check if same order type
      if (prev.order_type === curr.order_type) {
        if (prev.order_type === OrderType.BUY && curr.quantity_int > prev.quantity_int) {
          violations.push({
            trader: companyId,
            round: curr.round,
            issue: `Buy quantity increased from ${prev.quantity_int} to ${curr.quantity_int}`
          });
        } else if (prev.order_type === OrderType.SELL && curr.quantity_int < prev.quantity_int) {
          violations.push({
            trader: companyId,
            round: curr.round,
            issue: `Sell quantity decreased from ${prev.quantity_int} to ${curr.quantity_int}`
          });
        }
      }
    }
  });

  const buyViolations = violations.filter(v => v.issue.includes('Buy')).length;
  const sellViolations = violations.filter(v => v.issue.includes('Sell')).length;

  return {
    violations,
    summary: {
      totalViolations: violations.length,
      buyViolations,
      sellViolations
    }
  };
}
