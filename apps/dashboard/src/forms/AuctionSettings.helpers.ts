import { DeSettingsValue, DateTimeValue, Crud } from './AuctionSettings';

// Helper function to create a DateTimeValue from current time plus minutes
export function createDateTimeValueFromNow(minutesFromNow: number = 0): DateTimeValue {
  const date = new Date(Date.now() + minutesFromNow * 60 * 1000);
  return {
    day_of_month: date.getDate(),
    day_of_week: date.getDay(),
    hour: date.getHours(),
    minutes: date.getMinutes(),
    month: date.getMonth(),
    seconds: 0, // Set to 0 for cleaner display
    year: date.getFullYear(),
  };
}

// Create default settings for demo/testing
export function createDefaultDeSettingsValue(): DeSettingsValue {
  return {
    auction_name: "Test DE Auction - " + new Date().toLocaleDateString(),
    cost_multiplier: "1.0",
    excess_level_0_label: "Balanced",
    excess_level_1_label: "1-50 MW",
    excess_level_1_quantity: "50",
    excess_level_2_label: "51-100 MW", 
    excess_level_2_quantity: "100",
    excess_level_3_label: "101-200 MW",
    excess_level_3_quantity: "200",
    excess_level_4_label: ">200 MW",
    excess_level_4_quantity: "999999",
    price_change_initial: "1.00",
    price_change_post_reversal: "0.50",
    price_decimal_places: 2,
    price_label: "Price ($/MWh)",
    quantity_label: "Quantity (MW)",
    quantity_minimum: "10",
    quantity_step: "5",
    round_closed_min_secs: 10,
    round_open_min_secs: 60,
    round_orange_secs: 30,
    round_red_secs: 15,
    starting_price_announcement_mins: 15,
    starting_time: createDateTimeValueFromNow(30), // 30 minutes from now
    default_buyer_credit_limit: 10000000,
    default_seller_quantity_limit: "50",
    category_id: "1",
  };
}

// Create empty settings
export function createEmptyDeSettingsValue(): DeSettingsValue {
  return {
    auction_name: "",
    cost_multiplier: "",
    excess_level_0_label: "",
    excess_level_1_label: "",
    excess_level_1_quantity: "",
    excess_level_2_label: "",
    excess_level_2_quantity: "",
    excess_level_3_label: "",
    excess_level_3_quantity: "",
    excess_level_4_label: "",
    excess_level_4_quantity: "",
    price_change_initial: "",
    price_change_post_reversal: "",
    price_decimal_places: 0,
    price_label: "",
    quantity_label: "",
    quantity_minimum: "",
    quantity_step: "",
    round_closed_min_secs: 0,
    round_open_min_secs: 0,
    round_orange_secs: 0,
    round_red_secs: 0,
    starting_price_announcement_mins: 0,
    starting_time: null,
    default_buyer_credit_limit: 0,
    default_seller_quantity_limit: "",
    category_id: "",
  };
}

// Demo auction categories
export const demoAuctionCategories = [
  { id: "1", name: "Energy" },
  { id: "2", name: "Capacity" },
  { id: "3", name: "Ancillary Services" },
  { id: "4", name: "Transmission" },
  { id: "5", name: "Renewable Energy Credits" },
];
